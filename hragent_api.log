2025-07-29 14:27:58,852 - itsm_solution - INFO - Logging initialized
2025-07-29 14:27:58,852 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-29 14:27:58,852 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-29 14:27:58,852 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-29 14:27:58,852 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-29 14:27:58,852 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-29 14:27:58,852 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-29 14:27:58,852 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-29 14:27:58,852 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-29 14:27:58,852 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-29 14:27:58,852 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-29 14:27:58,852 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-29 14:27:58,852 - profile_agent - INFO - Enabling safety callbacks
2025-07-29 14:27:58,852 - profile_agent - INFO - Adding post-processing callback
2025-07-29 14:27:58,852 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-29 14:27:58,852 - profile_agent - INFO - Profile Agent created successfully
2025-07-29 14:27:58,852 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-29 14:27:58,852 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-29 14:27:58,858 - services.audit_service - INFO - Agent hr_root_agent already registered with ID 1
2025-07-29 14:27:58,858 - utils.audit_callbacks - INFO - Agent hr_root_agent registered with ID 1
2025-07-29 14:27:58,858 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-29 14:27:58,860 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-29 14:27:58,860 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-29 14:27:58,860 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-29 14:27:58,860 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-29 14:27:58,860 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-29 14:27:58,860 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-29 14:27:58,861 - services.audit_service - INFO - Agent hr_service_desk_agent already registered with ID 2
2025-07-29 14:27:58,861 - utils.audit_callbacks - INFO - Agent hr_service_desk_agent registered with ID 2
2025-07-29 14:27:58,861 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-29 14:27:58,862 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-29 14:27:58,862 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-29 14:27:58,862 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-29 14:27:58,862 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-29 14:27:58,862 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-29 14:27:58,862 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-29 14:27:58,862 - services.audit_service - INFO - Agent leave_management_agent already registered with ID 3
2025-07-29 14:27:58,862 - utils.audit_callbacks - INFO - Agent leave_management_agent registered with ID 3
2025-07-29 14:27:58,862 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-29 14:27:58,863 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-29 14:27:58,863 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-29 14:27:58,863 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-29 14:27:58,863 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-29 14:27:58,863 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-29 14:27:58,863 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-29 14:27:58,863 - services.audit_service - INFO - Agent attendance_management_agent already registered with ID 4
2025-07-29 14:27:58,863 - utils.audit_callbacks - INFO - Agent attendance_management_agent registered with ID 4
2025-07-29 14:27:58,863 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-29 14:27:58,865 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-29 14:27:58,865 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-29 14:27:58,865 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-29 14:27:58,865 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-29 14:27:58,865 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-29 14:27:58,865 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-29 14:27:58,865 - services.audit_service - INFO - Agent policy_agent already registered with ID 5
2025-07-29 14:27:58,865 - utils.audit_callbacks - INFO - Agent policy_agent registered with ID 5
2025-07-29 14:27:58,865 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-29 14:27:58,866 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-29 14:27:58,866 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-29 14:27:58,866 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-29 14:27:58,866 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-29 14:27:58,866 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-29 14:27:58,866 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-29 14:27:58,866 - services.audit_service - INFO - Agent profile_agent already registered with ID 6
2025-07-29 14:27:58,866 - utils.audit_callbacks - INFO - Agent profile_agent registered with ID 6
2025-07-29 14:27:58,866 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-29 14:27:58,866 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-29 14:27:58,866 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-29 14:27:58,866 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-29 14:27:58,866 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-29 14:27:58,866 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-29 14:27:58,866 - agents.root_agent - INFO - Audit trail setup complete
2025-07-29 14:27:58,866 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-29 14:27:58,867 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-29 14:27:58,867 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-29 14:27:58,867 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-29 14:27:58,867 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-29 14:27:58,893 - itsm_solution - INFO - Logging initialized
2025-07-29 14:27:58,893 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-29 14:27:58,893 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-29 14:27:58,893 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-29 14:27:58,893 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-29 14:27:58,893 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-29 14:27:58,893 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-29 14:27:58,893 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-29 14:27:58,893 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-29 14:27:58,893 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-29 14:27:58,893 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-29 14:27:58,893 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-29 14:27:58,893 - profile_agent - INFO - Enabling safety callbacks
2025-07-29 14:27:58,893 - profile_agent - INFO - Adding post-processing callback
2025-07-29 14:27:58,893 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-29 14:27:58,893 - profile_agent - INFO - Profile Agent created successfully
2025-07-29 14:27:58,894 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-29 14:27:58,894 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-29 14:27:58,894 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-29 14:27:58,894 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-29 14:27:58,894 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-29 14:27:58,894 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-29 14:27:58,894 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-29 14:27:58,894 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-29 14:27:58,894 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-29 14:27:58,894 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-29 14:27:58,895 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-29 14:27:58,895 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-29 14:27:58,895 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-29 14:27:58,895 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-29 14:27:58,895 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-29 14:27:58,895 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-29 14:27:58,895 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-29 14:27:58,895 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-29 14:27:58,895 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-29 14:27:58,895 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-29 14:27:58,895 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-29 14:27:58,895 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-29 14:27:58,895 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-29 14:27:58,895 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-29 14:27:58,896 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-29 14:27:58,896 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-29 14:27:58,896 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-29 14:27:58,896 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-29 14:27:58,896 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-29 14:27:58,896 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-29 14:27:58,896 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-29 14:27:58,896 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-29 14:27:58,896 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-29 14:27:58,896 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-29 14:27:58,896 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-29 14:27:58,896 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-29 14:27:58,896 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-29 14:27:58,896 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-29 14:27:58,897 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-29 14:27:58,897 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-29 14:27:58,897 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-29 14:27:58,897 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-29 14:27:58,897 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-29 14:27:58,897 - agents.root_agent - INFO - Audit trail setup complete
2025-07-29 14:27:58,897 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-29 14:27:58,897 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-29 14:27:58,897 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-29 14:27:58,897 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-29 14:27:58,897 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-29 14:28:54,250 - api_server - INFO - Setting Microsoft token in HR auth manager...
2025-07-29 14:28:54,251 - utils.hr_auth - INFO - Microsoft token set from frontend
2025-07-29 14:28:54,251 - api_server - INFO - Microsoft token set in HR auth manager
2025-07-29 14:28:54,251 - utils.hr_auth - INFO - WagonHR token set directly
2025-07-29 14:28:54,251 - api_server - INFO - WagonHR token set in HR auth manager successfully
2025-07-29 14:28:54,251 - api_server - INFO - Setting tenant context: Entity=MOURI Tech Limited (IN-MT-001)
2025-07-29 14:28:54,255 - utils.hr_api_client - INFO - Set tenant context: tenantId=mouritech, entityId=IN-MT-001, organizationId=None
2025-07-29 14:28:54,255 - utils.hr_api_client - INFO - Set tenant context: tenantId=mouritech, entityId=IN-MT-001, organizationId=None
2025-07-29 14:28:54,255 - api_server - INFO - Tenant/entity context set for API clients
2025-07-29 14:28:54,256 - services.session_service - WARNING - Session 12345 not found
2025-07-29 14:28:54,256 - runners.runner_service - WARNING - Session not found: user_id=12345, session_id=12345. Creating new session.
2025-07-29 14:28:54,256 - services.session_service - INFO - Creating session: app=hragent_api, user=12345, session=12345
2025-07-29 14:28:54,257 - services.session_service - INFO - Session created successfully: 12345
2025-07-29 14:28:54,257 - utils.context_manager - INFO - Created new context state for session 12345
2025-07-29 14:28:54,257 - utils.context_manager - INFO - Created new context state for session 12345
2025-07-29 14:28:54,257 - utils.context_manager - INFO - Detected topic: leave_management for message: apply for a vacation leave from Dec 24th, 2025 to ...
2025-07-29 14:28:54,257 - utils.context_manager - INFO - Suggested agent: leave_management_agent for topic: leave_management
2025-07-29 14:28:54,257 - runners.runner_service - INFO - Context switch detected: False
2025-07-29 14:28:54,258 - runners.runner_service - INFO - Routing recommendation: {'suggested_agent': 'leave_management_agent', 'detected_topic': 'leave_management', 'context_switch': False, 'current_topic': 'unknown', 'active_agent': 'hr_root_agent', 'agent_switching_frequency': 0, 'confidence': 0.****************, 'reasoning': 'Detected topic: leave_management; Recommended agent: leave_management_agent'}
2025-07-29 14:28:55,796 - runners.runner_service - ERROR - Failed to parse LLM response as JSON
2025-07-29 14:28:55,797 - runners.runner_service - INFO - Detected intent: {'agent': 'root_agent', 'type': 'general', 'confidence': 0.0, 'reason': 'Failed to parse response'}
2025-07-29 14:28:55,797 - runners.runner_service - INFO - Running message through runner 12345, 12345, parts=[Part(
  text='apply for a vacation leave from Dec 24th, 2025 to Dec 3oth, 2025 because you are going home for family vacation'
)] role='user'
2025-07-29 14:28:55,800 - utils.callbacks - INFO - Generated new session ID for audit: e51723d9-7944-4ace-aafb-d26caf3584e4
2025-07-29 14:28:55,800 - utils.callbacks - INFO - Using unknown_user as user ID for audit
2025-07-29 14:28:55,809 - services.audit_service - INFO - Session e51723d9-7944-4ace-aafb-d26caf3584e4 created for user unknown_user with agent 1
2025-07-29 14:28:59,273 - utils.callbacks - INFO - Starting combined post-processing for agent: hr_root_agent
2025-07-29 14:28:59,274 - utils.callbacks - INFO - Recorded response in session state for agent: hr_root_agent
2025-07-29 14:28:59,274 - utils.context_manager - INFO - Created new context state for session e51723d9-7944-4ace-aafb-d26caf3584e4
2025-07-29 14:28:59,274 - utils.context_manager - INFO - Detected topic: leave_management for message: apply for a vacation leave from Dec 24th, 2025 to ...
2025-07-29 14:28:59,274 - utils.context_manager - INFO - Recorded conversation turn for session e51723d9-7944-4ace-aafb-d26caf3584e4
2025-07-29 14:28:59,274 - utils.callbacks - INFO - Recorded conversation turn in context manager for session e51723d9-7944-4ace-aafb-d26caf3584e4
2025-07-29 14:28:59,275 - utils.callbacks - INFO - Running audit response callback for agent hr_root_agent
2025-07-29 14:28:59,277 - services.audit_service - INFO - Session e51723d9-7944-4ace-aafb-d26caf3584e4 already exists
2025-07-29 14:28:59,283 - utils.callbacks - INFO - Completed combined post-processing for agent hr_root_agent
2025-07-29 14:28:59,284 - runners.runner_service - INFO - finalizing event
2025-07-29 14:28:59,284 - runners.runner_service - INFO - finalizing event
2025-07-29 14:28:59,289 - utils.callbacks - INFO - HR agent context detected for agent: leave_management_agent
2025-07-29 14:28:59,290 - services.audit_service - INFO - Session e51723d9-7944-4ace-aafb-d26caf3584e4 already exists
2025-07-29 14:29:01,529 - utils.callbacks - INFO - Starting combined post-processing for agent: leave_management_agent
2025-07-29 14:29:01,530 - utils.callbacks - INFO - Recorded response in session state for agent: leave_management_agent
2025-07-29 14:29:01,530 - utils.context_manager - INFO - Recorded conversation turn for session e51723d9-7944-4ace-aafb-d26caf3584e4
2025-07-29 14:29:01,530 - utils.callbacks - INFO - Recorded conversation turn in context manager for session e51723d9-7944-4ace-aafb-d26caf3584e4
2025-07-29 14:29:01,530 - utils.callbacks - INFO - Processing response from HR agent: leave_management_agent
2025-07-29 14:29:01,530 - utils.callbacks - INFO - Running audit response callback for agent leave_management_agent
2025-07-29 14:29:01,532 - services.audit_service - INFO - Session e51723d9-7944-4ace-aafb-d26caf3584e4 already exists
2025-07-29 14:29:01,540 - utils.callbacks - INFO - Completed combined post-processing for agent leave_management_agent
2025-07-29 14:29:01,541 - runners.runner_service - INFO - finalizing event
2025-07-29 14:29:01,542 - services.audit_service - INFO - Session e51723d9-7944-4ace-aafb-d26caf3584e4 already exists
2025-07-29 14:29:01,550 - services.audit_service - INFO - Tool request_leave_intelligent registered for agent 3 with ID 272
2025-07-29 14:29:01,557 - runners.runner_service - INFO - finalizing event
2025-07-29 14:29:01,562 - utils.callbacks - INFO - HR agent context detected for agent: leave_management_agent
2025-07-29 14:29:01,562 - services.audit_service - INFO - Session e51723d9-7944-4ace-aafb-d26caf3584e4 already exists
2025-07-29 14:29:04,746 - utils.callbacks - INFO - Starting combined post-processing for agent: leave_management_agent
2025-07-29 14:29:04,746 - utils.callbacks - INFO - Recorded response in session state for agent: leave_management_agent
2025-07-29 14:29:04,747 - utils.context_manager - INFO - Recorded conversation turn for session e51723d9-7944-4ace-aafb-d26caf3584e4
2025-07-29 14:29:04,747 - utils.callbacks - INFO - Recorded conversation turn in context manager for session e51723d9-7944-4ace-aafb-d26caf3584e4
2025-07-29 14:29:04,747 - utils.callbacks - INFO - Processing response from HR agent: leave_management_agent
2025-07-29 14:29:04,747 - utils.callbacks - INFO - Running audit response callback for agent leave_management_agent
2025-07-29 14:29:04,748 - services.audit_service - INFO - Session e51723d9-7944-4ace-aafb-d26caf3584e4 already exists
2025-07-29 14:29:04,755 - utils.callbacks - INFO - Completed combined post-processing for agent leave_management_agent
2025-07-29 14:29:04,756 - runners.runner_service - INFO - finalizing event
2025-07-29 14:29:04,764 - utils.context_manager - INFO - Detected topic: leave_management for message: apply for a vacation leave from Dec 24th, 2025 to ...
2025-07-29 14:29:04,764 - utils.context_manager - INFO - Recorded conversation turn for session 12345
2025-07-29 14:29:45,179 - itsm_solution - INFO - Logging initialized
2025-07-29 14:29:45,179 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-29 14:29:45,179 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-29 14:29:45,179 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-29 14:29:45,179 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-29 14:29:45,179 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-29 14:29:45,179 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-29 14:29:45,179 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-29 14:29:45,179 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-29 14:29:45,179 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-29 14:29:45,179 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-29 14:29:45,179 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-29 14:29:45,179 - profile_agent - INFO - Enabling safety callbacks
2025-07-29 14:29:45,179 - profile_agent - INFO - Adding post-processing callback
2025-07-29 14:29:45,179 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-29 14:29:45,179 - profile_agent - INFO - Profile Agent created successfully
2025-07-29 14:29:45,179 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-29 14:29:45,179 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-29 14:29:45,185 - services.audit_service - INFO - Agent hr_root_agent already registered with ID 1
2025-07-29 14:29:45,185 - utils.audit_callbacks - INFO - Agent hr_root_agent registered with ID 1
2025-07-29 14:29:45,185 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-29 14:29:45,187 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-29 14:29:45,187 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-29 14:29:45,187 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-29 14:29:45,187 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-29 14:29:45,187 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-29 14:29:45,187 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-29 14:29:45,188 - services.audit_service - INFO - Agent hr_service_desk_agent already registered with ID 2
2025-07-29 14:29:45,188 - utils.audit_callbacks - INFO - Agent hr_service_desk_agent registered with ID 2
2025-07-29 14:29:45,188 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-29 14:29:45,188 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-29 14:29:45,188 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-29 14:29:45,188 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-29 14:29:45,188 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-29 14:29:45,188 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-29 14:29:45,188 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-29 14:29:45,189 - services.audit_service - INFO - Agent leave_management_agent already registered with ID 3
2025-07-29 14:29:45,189 - utils.audit_callbacks - INFO - Agent leave_management_agent registered with ID 3
2025-07-29 14:29:45,189 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-29 14:29:45,189 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-29 14:29:45,189 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-29 14:29:45,189 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-29 14:29:45,189 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-29 14:29:45,189 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-29 14:29:45,189 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-29 14:29:45,190 - services.audit_service - INFO - Agent attendance_management_agent already registered with ID 4
2025-07-29 14:29:45,190 - utils.audit_callbacks - INFO - Agent attendance_management_agent registered with ID 4
2025-07-29 14:29:45,190 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-29 14:29:45,190 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-29 14:29:45,191 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-29 14:29:45,191 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-29 14:29:45,191 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-29 14:29:45,191 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-29 14:29:45,191 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-29 14:29:45,191 - services.audit_service - INFO - Agent policy_agent already registered with ID 5
2025-07-29 14:29:45,191 - utils.audit_callbacks - INFO - Agent policy_agent registered with ID 5
2025-07-29 14:29:45,191 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-29 14:29:45,192 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-29 14:29:45,192 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-29 14:29:45,192 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-29 14:29:45,192 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-29 14:29:45,192 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-29 14:29:45,192 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-29 14:29:45,192 - services.audit_service - INFO - Agent profile_agent already registered with ID 6
2025-07-29 14:29:45,192 - utils.audit_callbacks - INFO - Agent profile_agent registered with ID 6
2025-07-29 14:29:45,192 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-29 14:29:45,193 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-29 14:29:45,193 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-29 14:29:45,193 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-29 14:29:45,193 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-29 14:29:45,193 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-29 14:29:45,193 - agents.root_agent - INFO - Audit trail setup complete
2025-07-29 14:29:45,193 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-29 14:29:45,193 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-29 14:29:45,193 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-29 14:29:45,193 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-29 14:29:45,193 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-29 14:29:45,218 - itsm_solution - INFO - Logging initialized
2025-07-29 14:29:45,218 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-29 14:29:45,218 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-29 14:29:45,218 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-29 14:29:45,218 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-29 14:29:45,218 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-29 14:29:45,218 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-29 14:29:45,218 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-29 14:29:45,218 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-29 14:29:45,218 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-29 14:29:45,218 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-29 14:29:45,218 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-29 14:29:45,218 - profile_agent - INFO - Enabling safety callbacks
2025-07-29 14:29:45,218 - profile_agent - INFO - Adding post-processing callback
2025-07-29 14:29:45,218 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-29 14:29:45,218 - profile_agent - INFO - Profile Agent created successfully
2025-07-29 14:29:45,218 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-29 14:29:45,219 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-29 14:29:45,219 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-29 14:29:45,219 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-29 14:29:45,219 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-29 14:29:45,219 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-29 14:29:45,219 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-29 14:29:45,219 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-29 14:29:45,219 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-29 14:29:45,219 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-29 14:29:45,220 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-29 14:29:45,220 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-29 14:29:45,220 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-29 14:29:45,220 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-29 14:29:45,220 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-29 14:29:45,220 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-29 14:29:45,220 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-29 14:29:45,220 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-29 14:29:45,220 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-29 14:29:45,220 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-29 14:29:45,220 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-29 14:29:45,220 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-29 14:29:45,220 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-29 14:29:45,220 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-29 14:29:45,221 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-29 14:29:45,221 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-29 14:29:45,221 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-29 14:29:45,221 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-29 14:29:45,221 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-29 14:29:45,221 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-29 14:29:45,221 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-29 14:29:45,221 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-29 14:29:45,221 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-29 14:29:45,221 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-29 14:29:45,221 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-29 14:29:45,221 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-29 14:29:45,221 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-29 14:29:45,221 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-29 14:29:45,222 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-29 14:29:45,222 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-29 14:29:45,222 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-29 14:29:45,222 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-29 14:29:45,222 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-29 14:29:45,222 - agents.root_agent - INFO - Audit trail setup complete
2025-07-29 14:29:45,222 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-29 14:29:45,222 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-29 14:29:45,222 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-29 14:29:45,222 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-29 14:29:45,222 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-29 14:29:49,510 - api_server - INFO - Setting Microsoft token in HR auth manager...
2025-07-29 14:29:49,510 - utils.hr_auth - INFO - Microsoft token set from frontend
2025-07-29 14:29:49,510 - api_server - INFO - Microsoft token set in HR auth manager
2025-07-29 14:29:49,510 - utils.hr_auth - INFO - WagonHR token set directly
2025-07-29 14:29:49,510 - api_server - INFO - WagonHR token set in HR auth manager successfully
2025-07-29 14:29:49,510 - api_server - INFO - Setting tenant context: Entity=MOURI Tech Limited (IN-MT-001)
2025-07-29 14:29:49,512 - utils.hr_api_client - INFO - Set tenant context: tenantId=mouritech, entityId=IN-MT-001, organizationId=None
2025-07-29 14:29:49,512 - utils.hr_api_client - INFO - Set tenant context: tenantId=mouritech, entityId=IN-MT-001, organizationId=None
2025-07-29 14:29:49,512 - api_server - INFO - Tenant/entity context set for API clients
2025-07-29 14:29:49,513 - services.session_service - WARNING - Session 12345 not found
2025-07-29 14:29:49,513 - runners.runner_service - WARNING - Session not found: user_id=12345, session_id=12345. Creating new session.
2025-07-29 14:29:49,513 - services.session_service - INFO - Creating session: app=hragent_api, user=12345, session=12345
2025-07-29 14:29:49,513 - services.session_service - INFO - Session created successfully: 12345
2025-07-29 14:29:49,513 - utils.context_manager - INFO - Created new context state for session 12345
2025-07-29 14:29:49,513 - utils.context_manager - INFO - Created new context state for session 12345
2025-07-29 14:29:49,513 - utils.context_manager - INFO - Detected topic: leave_management for message: apply for a vacation leave from Dec 24th, 2025 to ...
2025-07-29 14:29:49,513 - utils.context_manager - INFO - Suggested agent: leave_management_agent for topic: leave_management
2025-07-29 14:29:49,513 - runners.runner_service - INFO - Context switch detected: False
2025-07-29 14:29:49,513 - runners.runner_service - INFO - Routing recommendation: {'suggested_agent': 'leave_management_agent', 'detected_topic': 'leave_management', 'context_switch': False, 'current_topic': 'unknown', 'active_agent': 'hr_root_agent', 'agent_switching_frequency': 0, 'confidence': 0.****************, 'reasoning': 'Detected topic: leave_management; Recommended agent: leave_management_agent'}
2025-07-29 14:29:51,222 - runners.runner_service - ERROR - Failed to parse LLM response as JSON
2025-07-29 14:29:51,223 - runners.runner_service - INFO - Detected intent: {'agent': 'root_agent', 'type': 'general', 'confidence': 0.0, 'reason': 'Failed to parse response'}
2025-07-29 14:29:51,223 - runners.runner_service - INFO - Running message through runner 12345, 12345, parts=[Part(
  text='apply for a vacation leave from Dec 24th, 2025 to Dec 3oth, 2025 because you are going home for family vacation'
)] role='user'
2025-07-29 14:29:51,226 - utils.callbacks - INFO - Generated new session ID for audit: 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:29:51,226 - utils.callbacks - INFO - Using unknown_user as user ID for audit
2025-07-29 14:29:51,235 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f created for user unknown_user with agent 1
2025-07-29 14:29:54,241 - utils.callbacks - INFO - Starting combined post-processing for agent: hr_root_agent
2025-07-29 14:29:54,242 - utils.callbacks - INFO - Recorded response in session state for agent: hr_root_agent
2025-07-29 14:29:54,242 - utils.context_manager - INFO - Created new context state for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:29:54,242 - utils.context_manager - INFO - Detected topic: leave_management for message: apply for a vacation leave from Dec 24th, 2025 to ...
2025-07-29 14:29:54,242 - utils.context_manager - INFO - Recorded conversation turn for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:29:54,242 - utils.callbacks - INFO - Recorded conversation turn in context manager for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:29:54,243 - utils.callbacks - INFO - Running audit response callback for agent hr_root_agent
2025-07-29 14:29:54,245 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:29:54,253 - utils.callbacks - INFO - Completed combined post-processing for agent hr_root_agent
2025-07-29 14:29:54,254 - runners.runner_service - INFO - finalizing event
2025-07-29 14:29:54,255 - runners.runner_service - INFO - finalizing event
2025-07-29 14:29:54,260 - utils.callbacks - INFO - HR agent context detected for agent: leave_management_agent
2025-07-29 14:29:54,262 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:29:57,615 - utils.callbacks - INFO - Starting combined post-processing for agent: leave_management_agent
2025-07-29 14:29:57,616 - utils.callbacks - INFO - Recorded response in session state for agent: leave_management_agent
2025-07-29 14:29:57,616 - utils.context_manager - INFO - Recorded conversation turn for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:29:57,616 - utils.callbacks - INFO - Recorded conversation turn in context manager for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:29:57,616 - utils.callbacks - INFO - Processing response from HR agent: leave_management_agent
2025-07-29 14:29:57,616 - utils.callbacks - INFO - Running audit response callback for agent leave_management_agent
2025-07-29 14:29:57,618 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:29:57,627 - utils.callbacks - INFO - Completed combined post-processing for agent leave_management_agent
2025-07-29 14:29:57,627 - runners.runner_service - INFO - finalizing event
2025-07-29 14:29:57,629 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:29:57,635 - services.audit_service - INFO - Tool request_leave_intelligent registered for agent 3 with ID 273
2025-07-29 14:29:57,643 - runners.runner_service - INFO - finalizing event
2025-07-29 14:29:57,647 - utils.callbacks - INFO - HR agent context detected for agent: leave_management_agent
2025-07-29 14:29:57,647 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:30:01,121 - utils.callbacks - INFO - Starting combined post-processing for agent: leave_management_agent
2025-07-29 14:30:01,121 - utils.callbacks - INFO - Recorded response in session state for agent: leave_management_agent
2025-07-29 14:30:01,121 - utils.context_manager - INFO - Recorded conversation turn for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:30:01,121 - utils.callbacks - INFO - Recorded conversation turn in context manager for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:30:01,121 - utils.callbacks - INFO - Processing response from HR agent: leave_management_agent
2025-07-29 14:30:01,121 - utils.callbacks - INFO - Running audit response callback for agent leave_management_agent
2025-07-29 14:30:01,123 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:30:01,130 - utils.callbacks - INFO - Completed combined post-processing for agent leave_management_agent
2025-07-29 14:30:01,131 - runners.runner_service - INFO - finalizing event
2025-07-29 14:30:01,140 - utils.context_manager - INFO - Detected topic: leave_management for message: apply for a vacation leave from Dec 24th, 2025 to ...
2025-07-29 14:30:01,140 - utils.context_manager - INFO - Recorded conversation turn for session 12345
2025-07-29 14:40:46,937 - api_server - INFO - Setting Microsoft token in HR auth manager...
2025-07-29 14:40:46,938 - utils.hr_auth - INFO - Microsoft token set from frontend
2025-07-29 14:40:46,939 - api_server - INFO - Microsoft token set in HR auth manager
2025-07-29 14:40:46,939 - utils.hr_auth - INFO - WagonHR token set directly
2025-07-29 14:40:46,939 - api_server - INFO - WagonHR token set in HR auth manager successfully
2025-07-29 14:40:46,939 - api_server - INFO - Setting tenant context: Entity=MOURI Tech Limited (IN-MT-001)
2025-07-29 14:40:46,939 - utils.hr_api_client - INFO - Set tenant context: tenantId=mouritech, entityId=IN-MT-001, organizationId=None
2025-07-29 14:40:46,939 - utils.hr_api_client - INFO - Set tenant context: tenantId=mouritech, entityId=IN-MT-001, organizationId=None
2025-07-29 14:40:46,939 - api_server - INFO - Tenant/entity context set for API clients
2025-07-29 14:40:46,943 - utils.context_manager - INFO - Created new context state for session 12345
2025-07-29 14:40:46,945 - utils.context_manager - INFO - Created new context state for session 12345
2025-07-29 14:40:46,945 - utils.context_manager - INFO - Detected topic: leave_management for message: show me my pending leaves please...
2025-07-29 14:40:46,945 - utils.context_manager - INFO - Suggested agent: leave_management_agent for topic: leave_management
2025-07-29 14:40:46,945 - runners.runner_service - INFO - Context switch detected: False
2025-07-29 14:40:46,945 - runners.runner_service - INFO - Routing recommendation: {'suggested_agent': 'leave_management_agent', 'detected_topic': 'leave_management', 'context_switch': False, 'current_topic': 'unknown', 'active_agent': 'hr_root_agent', 'agent_switching_frequency': 0, 'confidence': 0.****************, 'reasoning': 'Detected topic: leave_management; Recommended agent: leave_management_agent'}
2025-07-29 14:40:49,431 - runners.runner_service - ERROR - Failed to parse LLM response as JSON
2025-07-29 14:40:49,431 - runners.runner_service - INFO - Detected intent: {'agent': 'root_agent', 'type': 'general', 'confidence': 0.0, 'reason': 'Failed to parse response'}
2025-07-29 14:40:49,432 - runners.runner_service - INFO - Running message through runner 12345, 12345, parts=[Part(
  text='show me my pending leaves please'
)] role='user'
2025-07-29 14:40:49,443 - utils.callbacks - INFO - HR agent context detected for agent: leave_management_agent
2025-07-29 14:40:49,451 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:40:51,428 - utils.callbacks - INFO - Starting combined post-processing for agent: leave_management_agent
2025-07-29 14:40:51,429 - utils.callbacks - INFO - Recorded response in session state for agent: leave_management_agent
2025-07-29 14:40:51,429 - utils.context_manager - INFO - Detected topic: leave_management for message: show me my pending leaves please...
2025-07-29 14:40:51,429 - utils.context_manager - INFO - Recorded conversation turn for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:40:51,429 - utils.callbacks - INFO - Recorded conversation turn in context manager for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:40:51,429 - utils.callbacks - INFO - Processing response from HR agent: leave_management_agent
2025-07-29 14:40:51,429 - utils.callbacks - INFO - Running audit response callback for agent leave_management_agent
2025-07-29 14:40:51,431 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:40:51,435 - utils.callbacks - INFO - Completed combined post-processing for agent leave_management_agent
2025-07-29 14:40:51,436 - runners.runner_service - INFO - finalizing event
2025-07-29 14:40:51,437 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:40:51,441 - services.audit_service - INFO - Tool get_pending_leaves registered for agent 3 with ID 274
2025-07-29 14:40:52,417 - runners.runner_service - INFO - finalizing event
2025-07-29 14:40:52,428 - utils.callbacks - INFO - HR agent context detected for agent: leave_management_agent
2025-07-29 14:40:52,430 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:40:55,630 - utils.callbacks - INFO - Starting combined post-processing for agent: leave_management_agent
2025-07-29 14:40:55,631 - utils.callbacks - INFO - Recorded response in session state for agent: leave_management_agent
2025-07-29 14:40:55,631 - utils.context_manager - INFO - Detected topic: leave_management for message: show me my pending leaves please...
2025-07-29 14:40:55,631 - utils.context_manager - INFO - Recorded conversation turn for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:40:55,631 - utils.callbacks - INFO - Recorded conversation turn in context manager for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:40:55,631 - utils.callbacks - INFO - Processing response from HR agent: leave_management_agent
2025-07-29 14:40:55,631 - utils.callbacks - INFO - Running audit response callback for agent leave_management_agent
2025-07-29 14:40:55,632 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:40:55,634 - utils.callbacks - INFO - Completed combined post-processing for agent leave_management_agent
2025-07-29 14:40:55,634 - runners.runner_service - INFO - finalizing event
2025-07-29 14:40:55,636 - utils.context_manager - INFO - Detected topic: leave_management for message: show me my pending leaves please...
2025-07-29 14:40:55,636 - utils.context_manager - INFO - Recorded conversation turn for session 12345
2025-07-29 14:41:11,832 - api_server - INFO - Setting Microsoft token in HR auth manager...
2025-07-29 14:41:11,833 - utils.hr_auth - INFO - Microsoft token set from frontend
2025-07-29 14:41:11,834 - api_server - INFO - Microsoft token set in HR auth manager
2025-07-29 14:41:11,834 - utils.hr_auth - INFO - WagonHR token set directly
2025-07-29 14:41:11,834 - api_server - INFO - WagonHR token set in HR auth manager successfully
2025-07-29 14:41:11,834 - api_server - INFO - Setting tenant context: Entity=MOURI Tech Limited (IN-MT-001)
2025-07-29 14:41:11,834 - utils.hr_api_client - INFO - Set tenant context: tenantId=mouritech, entityId=IN-MT-001, organizationId=None
2025-07-29 14:41:11,834 - utils.hr_api_client - INFO - Set tenant context: tenantId=mouritech, entityId=IN-MT-001, organizationId=None
2025-07-29 14:41:11,834 - api_server - INFO - Tenant/entity context set for API clients
2025-07-29 14:41:11,836 - utils.context_manager - INFO - Created new context state for session 12345
2025-07-29 14:41:11,839 - utils.context_manager - INFO - Created new context state for session 12345
2025-07-29 14:41:11,839 - utils.context_manager - INFO - Detected topic: leave_management for message: show me my available leave balance please...
2025-07-29 14:41:11,839 - utils.context_manager - INFO - Suggested agent: leave_management_agent for topic: leave_management
2025-07-29 14:41:11,839 - runners.runner_service - INFO - Context switch detected: False
2025-07-29 14:41:11,839 - runners.runner_service - INFO - Routing recommendation: {'suggested_agent': 'leave_management_agent', 'detected_topic': 'leave_management', 'context_switch': False, 'current_topic': 'unknown', 'active_agent': 'hr_root_agent', 'agent_switching_frequency': 0, 'confidence': 0.****************, 'reasoning': 'Detected topic: leave_management; Recommended agent: leave_management_agent'}
2025-07-29 14:41:14,426 - runners.runner_service - ERROR - Failed to parse LLM response as JSON
2025-07-29 14:41:14,427 - runners.runner_service - INFO - Detected intent: {'agent': 'root_agent', 'type': 'general', 'confidence': 0.0, 'reason': 'Failed to parse response'}
2025-07-29 14:41:14,427 - runners.runner_service - INFO - Running message through runner 12345, 12345, parts=[Part(
  text='show me my available leave balance please'
)] role='user'
2025-07-29 14:41:14,434 - utils.callbacks - INFO - HR agent context detected for agent: leave_management_agent
2025-07-29 14:41:14,436 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:41:17,588 - utils.callbacks - INFO - Starting combined post-processing for agent: leave_management_agent
2025-07-29 14:41:17,588 - utils.callbacks - INFO - Recorded response in session state for agent: leave_management_agent
2025-07-29 14:41:17,588 - utils.context_manager - INFO - Detected topic: leave_management for message: show me my available leave balance please...
2025-07-29 14:41:17,589 - utils.context_manager - INFO - Recorded conversation turn for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:41:17,589 - utils.callbacks - INFO - Recorded conversation turn in context manager for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:41:17,589 - utils.callbacks - INFO - Processing response from HR agent: leave_management_agent
2025-07-29 14:41:17,589 - utils.callbacks - INFO - Running audit response callback for agent leave_management_agent
2025-07-29 14:41:17,590 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:41:17,596 - utils.callbacks - INFO - Completed combined post-processing for agent leave_management_agent
2025-07-29 14:41:17,597 - runners.runner_service - INFO - finalizing event
2025-07-29 14:41:17,598 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:41:17,606 - services.audit_service - INFO - Tool get_leave_balance registered for agent 3 with ID 275
2025-07-29 14:41:18,784 - runners.runner_service - INFO - finalizing event
2025-07-29 14:41:18,793 - utils.callbacks - INFO - HR agent context detected for agent: leave_management_agent
2025-07-29 14:41:18,795 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:41:22,400 - utils.callbacks - INFO - Starting combined post-processing for agent: leave_management_agent
2025-07-29 14:41:22,401 - utils.callbacks - INFO - Recorded response in session state for agent: leave_management_agent
2025-07-29 14:41:22,401 - utils.context_manager - INFO - Detected topic: leave_management for message: show me my available leave balance please...
2025-07-29 14:41:22,401 - utils.context_manager - INFO - Recorded conversation turn for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:41:22,401 - utils.callbacks - INFO - Recorded conversation turn in context manager for session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f
2025-07-29 14:41:22,401 - utils.callbacks - INFO - Processing response from HR agent: leave_management_agent
2025-07-29 14:41:22,401 - utils.callbacks - INFO - Running audit response callback for agent leave_management_agent
2025-07-29 14:41:22,403 - services.audit_service - INFO - Session 89d61b66-0eaf-4d2c-8d99-22ed9da54b7f already exists
2025-07-29 14:41:22,408 - utils.callbacks - INFO - Completed combined post-processing for agent leave_management_agent
2025-07-29 14:41:22,408 - runners.runner_service - INFO - finalizing event
2025-07-29 14:41:22,413 - utils.context_manager - INFO - Detected topic: leave_management for message: show me my available leave balance please...
2025-07-29 14:41:22,413 - utils.context_manager - INFO - Recorded conversation turn for session 12345
2025-07-29 20:57:41,625 - itsm_solution - INFO - Logging initialized
2025-07-29 20:57:41,626 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-29 20:57:41,626 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-29 20:57:41,626 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-29 20:57:41,626 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-29 20:57:41,626 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-29 20:57:41,626 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-29 20:57:41,626 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-29 20:57:41,626 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-29 20:57:41,626 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-29 20:57:41,626 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-29 20:57:41,626 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-29 20:57:41,626 - profile_agent - INFO - Enabling safety callbacks
2025-07-29 20:57:41,626 - profile_agent - INFO - Adding post-processing callback
2025-07-29 20:57:41,626 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-29 20:57:41,626 - profile_agent - INFO - Profile Agent created successfully
2025-07-29 20:57:41,626 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-29 20:57:41,626 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-29 20:57:41,632 - services.audit_service - INFO - Agent hr_root_agent already registered with ID 1
2025-07-29 20:57:41,632 - utils.audit_callbacks - INFO - Agent hr_root_agent registered with ID 1
2025-07-29 20:57:41,632 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-29 20:57:41,634 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-29 20:57:41,634 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-29 20:57:41,634 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-29 20:57:41,634 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-29 20:57:41,634 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-29 20:57:41,634 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-29 20:57:41,635 - services.audit_service - INFO - Agent hr_service_desk_agent already registered with ID 2
2025-07-29 20:57:41,635 - utils.audit_callbacks - INFO - Agent hr_service_desk_agent registered with ID 2
2025-07-29 20:57:41,635 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-29 20:57:41,635 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-29 20:57:41,635 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-29 20:57:41,635 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-29 20:57:41,635 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-29 20:57:41,635 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-29 20:57:41,636 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-29 20:57:41,636 - services.audit_service - INFO - Agent leave_management_agent already registered with ID 3
2025-07-29 20:57:41,636 - utils.audit_callbacks - INFO - Agent leave_management_agent registered with ID 3
2025-07-29 20:57:41,636 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-29 20:57:41,636 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-29 20:57:41,637 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-29 20:57:41,637 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-29 20:57:41,637 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-29 20:57:41,637 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-29 20:57:41,637 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-29 20:57:41,637 - services.audit_service - INFO - Agent attendance_management_agent already registered with ID 4
2025-07-29 20:57:41,637 - utils.audit_callbacks - INFO - Agent attendance_management_agent registered with ID 4
2025-07-29 20:57:41,637 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-29 20:57:41,638 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-29 20:57:41,638 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-29 20:57:41,638 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-29 20:57:41,638 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-29 20:57:41,638 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-29 20:57:41,638 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-29 20:57:41,638 - services.audit_service - INFO - Agent policy_agent already registered with ID 5
2025-07-29 20:57:41,638 - utils.audit_callbacks - INFO - Agent policy_agent registered with ID 5
2025-07-29 20:57:41,638 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-29 20:57:41,639 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-29 20:57:41,639 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-29 20:57:41,639 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-29 20:57:41,639 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-29 20:57:41,639 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-29 20:57:41,639 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-29 20:57:41,639 - services.audit_service - INFO - Agent profile_agent already registered with ID 6
2025-07-29 20:57:41,639 - utils.audit_callbacks - INFO - Agent profile_agent registered with ID 6
2025-07-29 20:57:41,639 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-29 20:57:41,639 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-29 20:57:41,639 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-29 20:57:41,640 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-29 20:57:41,640 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-29 20:57:41,640 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-29 20:57:41,640 - agents.root_agent - INFO - Audit trail setup complete
2025-07-29 20:57:41,640 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-29 20:57:41,640 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-29 20:57:41,640 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-29 20:57:41,640 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-29 20:57:41,640 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
2025-07-29 20:57:41,664 - itsm_solution - INFO - Logging initialized
2025-07-29 20:57:41,664 - main - INFO - Initializing HRAgent Application with app_name=hragent_api
2025-07-29 20:57:41,664 - agents.hr_service_desk_agent - INFO - Creating HR Service Desk Agent with model gemini-2.0-flash
2025-07-29 20:57:41,664 - agents.hr_service_desk_agent - INFO - HR Service Desk Agent created successfully
2025-07-29 20:57:41,664 - agents.leave_management_agent - INFO - Creating Leave Management Agent with model gemini-2.0-flash
2025-07-29 20:57:41,664 - agents.leave_management_agent - INFO - Leave Management Agent created successfully
2025-07-29 20:57:41,664 - agents.attendance_management_agent - INFO - Creating Attendance Management Agent with model gemini-2.0-flash
2025-07-29 20:57:41,664 - agents.attendance_management_agent - INFO - Attendance Management Agent created successfully
2025-07-29 20:57:41,664 - agents.policy_agent - INFO - Creating Policy Agent with model gemini-2.0-flash
2025-07-29 20:57:41,664 - agents.policy_agent - INFO - Policy Agent created successfully
2025-07-29 20:57:41,664 - profile_agent - INFO - Creating Profile Agent with model gemini-2.0-flash
2025-07-29 20:57:41,664 - profile_agent - INFO - Profile tools configured: get_employee_info, get_employee_work_profile
2025-07-29 20:57:41,664 - profile_agent - INFO - Enabling safety callbacks
2025-07-29 20:57:41,664 - profile_agent - INFO - Adding post-processing callback
2025-07-29 20:57:41,664 - profile_agent - INFO - Initializing Profile Agent with configuration
2025-07-29 20:57:41,664 - profile_agent - INFO - Profile Agent created successfully
2025-07-29 20:57:41,664 - agents.root_agent - INFO - Setting up audit trail for all agents
2025-07-29 20:57:41,664 - utils.audit_setup - INFO - Setting up audit trail for agent hr_root_agent
2025-07-29 20:57:41,664 - utils.audit_setup - INFO - Registering system prompt for agent hr_root_agent
2025-07-29 20:57:41,665 - services.audit_service - INFO - Updated existing prompt for agent 1 with ID 1
2025-07-29 20:57:41,665 - utils.audit_setup - INFO - System prompt registered with ID 1
2025-07-29 20:57:41,665 - utils.callbacks - INFO - Agent hr_root_agent registered for audit callbacks
2025-07-29 20:57:41,665 - utils.audit_setup - INFO - Registered agent hr_root_agent for audit callbacks
2025-07-29 20:57:41,665 - utils.audit_setup - INFO - Audit trail set up for agent hr_root_agent
2025-07-29 20:57:41,665 - utils.audit_setup - INFO - Setting up audit trail for agent hr_service_desk_agent
2025-07-29 20:57:41,665 - utils.audit_setup - INFO - Registering system prompt for agent hr_service_desk_agent
2025-07-29 20:57:41,665 - services.audit_service - INFO - Updated existing prompt for agent 2 with ID 2
2025-07-29 20:57:41,665 - utils.audit_setup - INFO - System prompt registered with ID 2
2025-07-29 20:57:41,665 - utils.callbacks - INFO - Agent hr_service_desk_agent registered for audit callbacks
2025-07-29 20:57:41,665 - utils.audit_setup - INFO - Registered agent hr_service_desk_agent for audit callbacks
2025-07-29 20:57:41,665 - utils.audit_setup - INFO - Audit trail set up for agent hr_service_desk_agent
2025-07-29 20:57:41,665 - utils.audit_setup - INFO - Setting up audit trail for agent leave_management_agent
2025-07-29 20:57:41,665 - utils.audit_setup - INFO - Registering system prompt for agent leave_management_agent
2025-07-29 20:57:41,666 - services.audit_service - INFO - Updated existing prompt for agent 3 with ID 3
2025-07-29 20:57:41,666 - utils.audit_setup - INFO - System prompt registered with ID 3
2025-07-29 20:57:41,666 - utils.callbacks - INFO - Agent leave_management_agent registered for audit callbacks
2025-07-29 20:57:41,666 - utils.audit_setup - INFO - Registered agent leave_management_agent for audit callbacks
2025-07-29 20:57:41,666 - utils.audit_setup - INFO - Audit trail set up for agent leave_management_agent
2025-07-29 20:57:41,666 - utils.audit_setup - INFO - Setting up audit trail for agent attendance_management_agent
2025-07-29 20:57:41,666 - utils.audit_setup - INFO - Registering system prompt for agent attendance_management_agent
2025-07-29 20:57:41,667 - services.audit_service - INFO - Updated existing prompt for agent 4 with ID 4
2025-07-29 20:57:41,667 - utils.audit_setup - INFO - System prompt registered with ID 4
2025-07-29 20:57:41,667 - utils.callbacks - INFO - Agent attendance_management_agent registered for audit callbacks
2025-07-29 20:57:41,667 - utils.audit_setup - INFO - Registered agent attendance_management_agent for audit callbacks
2025-07-29 20:57:41,667 - utils.audit_setup - INFO - Audit trail set up for agent attendance_management_agent
2025-07-29 20:57:41,667 - utils.audit_setup - INFO - Setting up audit trail for agent policy_agent
2025-07-29 20:57:41,667 - utils.audit_setup - INFO - Registering system prompt for agent policy_agent
2025-07-29 20:57:41,667 - services.audit_service - INFO - Updated existing prompt for agent 5 with ID 5
2025-07-29 20:57:41,667 - utils.audit_setup - INFO - System prompt registered with ID 5
2025-07-29 20:57:41,667 - utils.callbacks - INFO - Agent policy_agent registered for audit callbacks
2025-07-29 20:57:41,667 - utils.audit_setup - INFO - Registered agent policy_agent for audit callbacks
2025-07-29 20:57:41,667 - utils.audit_setup - INFO - Audit trail set up for agent policy_agent
2025-07-29 20:57:41,667 - utils.audit_setup - INFO - Setting up audit trail for agent profile_agent
2025-07-29 20:57:41,667 - utils.audit_setup - INFO - Registering system prompt for agent profile_agent
2025-07-29 20:57:41,668 - services.audit_service - INFO - Updated existing prompt for agent 6 with ID 6
2025-07-29 20:57:41,668 - utils.audit_setup - INFO - System prompt registered with ID 6
2025-07-29 20:57:41,668 - utils.callbacks - INFO - Agent profile_agent registered for audit callbacks
2025-07-29 20:57:41,668 - utils.audit_setup - INFO - Registered agent profile_agent for audit callbacks
2025-07-29 20:57:41,668 - utils.audit_setup - INFO - Audit trail set up for agent profile_agent
2025-07-29 20:57:41,668 - agents.root_agent - INFO - Audit trail setup complete
2025-07-29 20:57:41,668 - main - INFO - Using Enhanced In-Memory Session Service with ADK standards
2025-07-29 20:57:41,668 - services.session_service - INFO - Enhanced session service initialized following ADK standards
2025-07-29 20:57:41,668 - main - INFO - Using Enhanced In-Memory Memory Service for cross-session knowledge
2025-07-29 20:57:41,668 - services.memory_service - INFO - Enhanced memory service initialized following ADK standards
2025-07-29 20:57:41,668 - runners.runner_service - INFO - HR AI Assistant Runner Service initialized with app_name=hragent_api, session_service=EnhancedInMemorySessionService
