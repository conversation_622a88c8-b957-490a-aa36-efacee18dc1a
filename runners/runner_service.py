"""
Runner service implementation.

This module provides a custom runner service for the ITSM solution.
"""

import asyncio
import logging
import os
import time
import uuid
import json
from typing import Dict, Any, Optional, AsyncGenerator, List
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from google.adk.agents import Agent
from google.adk.runners import Runner
from google.adk.sessions import Session, BaseSessionService, InMemorySessionService
from google.genai import types
from google.genai.errors import ServerError

from services.session_service import EnhancedInMemorySessionService
from services.memory_service import EnhancedInMemoryMemoryService

# Set up logger
logger = logging.getLogger(__name__)

# Maximum number of retries for 503 errors
MAX_RETRIES = 3
# Initial delay between retries in seconds
INITIAL_RETRY_DELAY = 1
# Maximum delay between retries in seconds
MAX_RETRY_DELAY = 10


class ItsmsRunnerService:
    """ITSM Runner Service for managing agent execution."""

    def __init__(
        self,
        root_agent: Agent,
        app_name: str = "hr_solution",
        session_service: Optional[object] = None,
        memory_service: Optional[object] = None
    ):
        """Initialize the ITSM Runner Service following ADK standards.

        Args:
            root_agent: The root agent to use for handling requests
            app_name: The name of the application
            session_service: The session service to use, or None to create a new one
            memory_service: The memory service to use for long-term knowledge storage
        """
        self.root_agent = root_agent
        self.app_name = app_name

        # Use provided session service or create a default one
        if session_service is None:
            self.session_service = EnhancedInMemorySessionService()
        else:
            self.session_service = session_service

        # Use provided memory service or create a default one
        if memory_service is None:
            self.memory_service = EnhancedInMemoryMemoryService()
        else:
            self.memory_service = memory_service

        # Create the runner with both session and memory services
        self.runner = Runner(
            agent=root_agent,
            app_name=app_name,
            session_service=self.session_service,
            memory_service=self.memory_service
        )

        self.active_sessions = {}

        # Log initialization with session service type
        service_type = type(self.session_service).__name__
        logger.info(f"HR AI Assistant Runner Service initialized with app_name={app_name}, session_service={service_type}")

    async def create_session(
        self,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        initial_state: Optional[Dict[str, Any]] = None
    ) -> Dict[str, str]:
        """Create a new session.

        Args:
            user_id: The user ID, or None to generate a random one
            session_id: The session ID, or None to generate a random one
            initial_state: Optional initial state for the session

        Returns:
            A dictionary with the user_id and session_id
        """
        # Generate IDs if not provided
        if not user_id:
            user_id = f"user_{uuid.uuid4()}"

        if not session_id:
            session_id = f"session_{uuid.uuid4()}"

        # Create session
        session = await self.session_service.create_session(
            app_name=self.app_name,
            user_id=user_id,
            session_id=session_id,
            state=initial_state or {}
        )

        print("session created: ", session.id)

        # Track active session
        self.active_sessions[session_id] = {
            "user_id": user_id,
            "created_at": time.time(),
            "last_activity": time.time()
        }

        print("active sessions found")

        logger.info(f"Created new session: user_id={user_id}, session_id={session_id}")
        return {
            "user_id": user_id,
            "session_id": session_id
        }

    async def get_session(self, user_id: str, session_id: str) -> Optional[Session]:
        """Get an existing session.

        Args:
            user_id: The user ID
            session_id: The session ID

        Returns:
            The session if found, None otherwise
        """
        session = await self.session_service.get_session(
            app_name=self.app_name,
            user_id=user_id,
            session_id=session_id
        )
        # If session not found, create a new one
        if not session:
            logger.warning(f"Session not found: user_id={user_id}, session_id={session_id}. Creating new session.")
            session = await self.session_service.create_session(
                app_name=self.app_name,
                user_id=user_id,
                session_id=session_id,
                state={}
            )
        return session

    async def add_session_to_memory(self, session: Session) -> None:
        """Add completed session to long-term memory following ADK standards.

        Args:
            session: The session to add to memory
        """
        try:
            await self.memory_service.add_session_to_memory(session)
            logger.info(f"Added session {session.id} to long-term memory")
        except Exception as e:
            logger.error(f"Failed to add session to memory: {e}")

    async def search_memory(self, user_id: str, query: str, max_results: int = 5):
        """Search long-term memory for relevant information.

        Args:
            user_id: The user ID
            query: The search query
            max_results: Maximum number of results to return

        Returns:
            SearchMemoryResponse with relevant results
        """
        try:
            return await self.memory_service.search_memory(
                app_name=self.app_name,
                user_id=user_id,
                query=query,
                max_results=max_results
            )
        except Exception as e:
            logger.error(f"Failed to search memory: {e}")
            return None

    @retry(
        stop=stop_after_attempt(MAX_RETRIES),
        wait=wait_exponential(multiplier=INITIAL_RETRY_DELAY, max=MAX_RETRY_DELAY),
        retry=retry_if_exception_type(ServerError),
        reraise=True
    )
    async def _process_with_retry(
        self,
        user_id: str,
        session_id: str,
        content: types.Content
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Process message with retry logic for 503 errors.

        Args:
            user_id: The user ID
            session_id: The session ID
            content: The message content to process

        Yields:
            Dictionary with response details for each event
        """
        async for event in self.runner.run_async(
            user_id=user_id,
            session_id=session_id,
            new_message=content
        ):
            # Extract text content if available
            text_content = None
            try:
                if (hasattr(event, 'content') and event.content and
                    hasattr(event.content, 'parts') and event.content.parts and
                    len(event.content.parts) > 0 and
                    hasattr(event.content.parts[0], 'text') and event.content.parts[0].text):
                    text_content = event.content.parts[0].text

                    # Filter out internal tool execution details
                    if text_content and self._is_tool_execution_detail(text_content):
                        logger.warning(f"Filtering out tool execution details: {text_content[:100]}...")
                        text_content = self._get_filtered_response(text_content)

            except (AttributeError, IndexError, TypeError):
                text_content = None

            # Safely get event type name
            try:
                event_type = type(event).__name__
            except AttributeError:
                event_type = "UnknownEvent"

            response = {
                "type": event_type,
                "author": getattr(event, 'author', 'unknown'),
                "is_final": event.is_final_response() if hasattr(event, 'is_final_response') else False,
                "timestamp": time.time()
            }

            if text_content:
                response["content"] = text_content

            logger.info("finalizing event")
            if hasattr(event, 'is_final_response') and event.is_final_response():
                print("final response found")
                # Add user session data to final response
                session = await self.get_session(user_id, session_id)
                if session and hasattr(session, 'state') and session.state:
                    # Get relevant state info (excluding potentially large objects)
                    safe_state = {}
                    for key, value in session.state.items():
                        if key in ["last_ticket_id", "session_metadata"]:
                            safe_state[key] = value

                    if safe_state:
                        response["session_data"] = safe_state
            yield response

    async def process_message(
        self,
        message: str,
        user_id: str,
        session_id: str,
        api_auth_token: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Process a user message.

        Args:
            message: The user message
            user_id: The user ID
            session_id: The session ID
            api_auth_token: API authentication token information

        Yields:
            Dictionary with response details for each event
        """

        # Check if session exists and create it if it doesn't
        session = await self.get_session(user_id, session_id)

        print("session found: ", session.id)

        # Store API auth token in session state if provided
        if api_auth_token and session and hasattr(session, 'state'):
            print("store api auth token in session state")
            if not hasattr(session.state, '__setitem__'):
                # If state is not a dict, make it one
                session.state = {}
            session.state["api_auth_token"] = api_auth_token

        # Update active session tracking
        if session_id in self.active_sessions:
            print("update active session tracking")
            self.active_sessions[session_id]["last_activity"] = time.time()
        else:
            # Add to active sessions if not already tracked
            print("add to active sessions if not already tracked")
            self.active_sessions[session_id] = {
                "user_id": user_id,
                "created_at": time.time(),
                "last_activity": time.time()
            }

        # Initialize context manager for chat history
        from utils.context_manager import EnhancedContextManager
        context_manager = EnhancedContextManager()
        
        # Get conversation context for enhanced processing
        conversation_context = context_manager.get_conversation_context(session_id, last_n_turns=3)
        context_summary = context_manager.get_context_summary(session_id)
        
        # Detect if this is a context switch
        context_switch = context_manager.detect_context_switch(session_id, message)
        
        # Get routing recommendation based on context
        routing_recommendation = context_manager.get_routing_recommendation(session_id, message)
        
        logger.info(f"Context switch detected: {context_switch}")
        logger.info(f"Routing recommendation: {routing_recommendation}")

        # Use LLM intelligence to detect intent and route to appropriate agent
        try:
            intent = await self._analyze_intent(message)
            logger.info(f"Detected intent: {intent}")

            if intent['agent'] != 'root_agent':
                logger.info(f"Routing to {intent['agent']} with enhanced prompt")
                
                # Enhance the message with context information
                context_enhanced_message = f"Previous conversation context:\n{conversation_context}\n\nPlease transfer this {intent['type']} request to the {intent['agent']}: {message}"
                content = types.Content(role='user', parts=[types.Part(text=context_enhanced_message)])

                try:
                    logger.info(f"Routing through root agent to {intent['agent']}")
                    async for event in self._process_with_retry(user_id, session_id, content):
                        yield event
                        if event.get("is_final", False):
                            return

                except Exception as agent_error:
                    logger.error(f"Error routing to {intent['agent']}: {str(agent_error)}")
                    # Fall through to normal processing instead of returning error

        except Exception as e:
            logger.error(f"Error in LLM-guided routing: {str(e)}")
            # Fall through to normal AI processing if LLM intent detection fails

        # Prepare the user message for normal processing with context
        # If we have conversation context, include it in the message
        if conversation_context and conversation_context != "No previous conversation history.":
            enhanced_message = f"Previous conversation context:\n{conversation_context}\n\nCurrent message: {message}"
            logger.info(f"Enhanced message with context for session {session_id}")
        else:
            enhanced_message = message
            
        content = types.Content(role='user', parts=[types.Part(text=enhanced_message)])

        # Process through runner
        try:
            logger.info(f"Running message through runner {user_id}, {session_id}, {content}")
            async for event in self._process_with_retry(user_id, session_id, content):
                yield event

        except ServerError as e:
            error_message = str(e)
            if "503" in error_message:
                logger.error(f"Service temporarily unavailable after {MAX_RETRIES} retries: {error_message}")
                yield {
                    "type": "message",
                    "content": "I apologize, but I'm currently experiencing high demand. Please try your request again in a moment. I'll be happy to help you as soon as I'm available.",
                    "is_final": True,
                    "timestamp": time.time()
                }
            elif "429" in error_message:
                logger.error(f"Rate limit exceeded: {error_message}")
                yield {
                    "type": "message",
                    "content": "I'm receiving too many requests at the moment. Please wait a moment before trying again.",
                    "is_final": True,
                    "timestamp": time.time()
                }
            elif "401" in error_message or "403" in error_message:
                logger.error(f"Authentication error: {error_message}")
                yield {
                    "type": "message",
                    "content": "I'm having trouble authenticating your request. Please try logging in again or contact support if the issue persists.",
                    "is_final": True,
                    "timestamp": time.time()
                }
            else:
                logger.error(f"Server error: {error_message}")
                yield {
                    "type": "message",
                    "content": "I'm experiencing some technical difficulties. Please try again in a few moments. If the problem persists, our support team will be notified.",
                    "is_final": True,
                    "timestamp": time.time()
                }
        except Exception as e:
            import traceback
            logger.error(f"Error processing message: {str(e)}")
            logger.error(f"Full traceback: {traceback.format_exc()}")

            # Check for specific error types and handle accordingly
            error_str = str(e)

            # Handle quota exceeded errors (429)
            if "429" in error_str or "RESOURCE_EXHAUSTED" in error_str or "quota" in error_str.lower():
                logger.error(f"Quota exceeded: {error_str}")
                yield {
                    "type": "message",
                    "content": "I'm currently experiencing high demand and have reached my daily limit. Please try again tomorrow or contact support if you need immediate assistance.",
                    "is_final": True,
                    "timestamp": time.time()
                }
            # Handle rate limit errors
            elif "rate limit" in error_str.lower() or "too many requests" in error_str.lower():
                logger.error(f"Rate limit exceeded: {error_str}")
                yield {
                    "type": "message",
                    "content": "I'm receiving too many requests at the moment. Please wait a few minutes before trying again.",
                    "is_final": True,
                    "timestamp": time.time()
                }
            # Handle authentication errors
            elif "401" in error_str or "403" in error_str or "authentication" in error_str.lower():
                logger.error(f"Authentication error: {error_str}")
                yield {
                    "type": "message",
                    "content": "I'm having trouble authenticating your request. Please try logging in again or contact support if the issue persists.",
                    "is_final": True,
                    "timestamp": time.time()
                }
            # Handle API key errors
            elif "API key not valid" in error_str or "invalid api key" in error_str.lower():
                logger.warning("API key not valid, using mock data instead")
                os.environ["USE_MOCK_DATA"] = "true"

                # Create a mock event with a more helpful response
                text_content = "I'm operating in offline mode due to API key issues, but I can still help with basic HR inquiries. I can provide information about leave balances, leave applications, attendance records, and HR policies. How can I assist you today?"

                response = {
                    "type": "MockEvent",
                    "content": text_content,
                    "is_final": True,
                    "timestamp": time.time(),
                    "content": text_content
                }

                logger.info("Yielding mock response due to API key error")
                yield response
            # Handle network/timeout errors
            elif "timeout" in error_str.lower() or "network" in error_str.lower() or "connection" in error_str.lower():
                logger.error(f"Network/timeout error: {error_str}")
                yield {
                    "type": "message",
                    "content": "I'm experiencing connection issues. Please check your internet connection and try again in a moment.",
                    "is_final": True,
                    "timestamp": time.time()
                }
            # Handle model-specific errors
            elif "model" in error_str.lower() or "generation" in error_str.lower():
                logger.error(f"Model error: {error_str}")
                yield {
                    "type": "message",
                    "content": "I'm experiencing technical difficulties with my language model. Please try again in a few moments.",
                    "is_final": True,
                    "timestamp": time.time()
                }
            else:
                # For other errors, return a generic user-friendly error response
                logger.error(f"Unknown error: {error_str}")
                yield {
                    "type": "message",
                    "content": "I'm experiencing some technical difficulties. Please try again in a few moments. If the problem persists, our support team will be notified.",
                    "is_final": True,
                    "timestamp": time.time()
                }

    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get all active sessions.

        Returns:
            Dictionary of active sessions
        """
        return self.active_sessions.copy()

    def get_session_events(self, session_id: str) -> List[Dict[str, Any]]:
        """Get events for a specific session.

        Args:
            session_id: The session ID

        Returns:
            List of events for the session
        """
        return self.session_service.get_session_events(session_id)

    async def _analyze_intent(self, message: str) -> Dict[str, Any]:
        """Use LLM to analyze message intent and determine routing.

        Args:
            message: The user message to analyze

        Returns:
            Dictionary with intent analysis results including:
            {
                "agent": "agent_name",  # The specialized agent to route to
                "type": "intent_type",  # The type of request
                "confidence": 0.0-1.0,  # Confidence score
                "reason": "explanation"  # Brief explanation
            }
        """
        try:
            # Use a lightweight LLM call for intent detection
            import google.generativeai as genai
            import os

            # Configure the model for intent detection
            
            genai.configure(api_key=os.environ.get('GOOGLE_API_KEY'))
            model = genai.GenerativeModel('gemini-2.5-flash')

            # Create an intelligent, semantic-based intent detection prompt
            intent_prompt = f"""
You are an expert HR assistant router with advanced semantic understanding. Analyze the user's message to understand their underlying intent and route to the most appropriate specialized agent.

Available agents:
- leave_management_agent: Leave requests, balances, approvals, holidays, time-off management
- profile_agent: Employee information, job details, team structure, organizational data
- attendance_management_agent: Attendance tracking, check-in/out, time tracking, presence
- policy_agent: HR policies, procedures, guidelines, rules, compliance, company standards
- hr_service_desk_agent: General HR support, complex multi-domain queries
- root_agent: Non-HR queries or truly ambiguous requests

**SEMANTIC INTENT PATTERNS:**

**POLICY AGENT** - Route here when the user's intent matches these patterns:
1. **Information Seeking About Standards/Rules:**
   - Asking what is allowed, permitted, prohibited, or acceptable
   - Seeking company standards, guidelines, procedures, or best practices
   - Inquiring about compliance, regulations, or official requirements

2. **Process and Procedure Inquiries:**
   - "How to" questions about company processes or workflows
   - Requests for step-by-step procedures or instructions
   - Questions about proper methods or approaches for company activities

3. **Document and Requirement Questions:**
   - Asking about required documents, materials, or submissions
   - Questions about prerequisites, qualifications, or conditions
   - Inquiries about what needs to be provided or prepared

4. **Timeline and Duration Inquiries:**
   - Questions about how long company processes take
   - Timeline expectations for procedures or activities
   - Duration-related questions about company operations

5. **Permission and Authorization Requests:**
   - Questions seeking permission or authorization
   - Inquiries about whether something is allowed or acceptable
   - Questions about rights, privileges, or entitlements

6. **New Employee/Onboarding Questions:**
   - Questions from new joiners about company processes
   - Onboarding-related inquiries about requirements or procedures
   - Initial setup, preparation, or orientation questions

**CORE PRINCIPLE:** Focus on the user's underlying INTENT and NEED, not specific keywords. A policy question can be phrased in countless ways but will always involve seeking information about company standards, processes, requirements, or permissions.

**SEMANTIC ROUTING EXAMPLES:**

**Leave Management Intent Patterns:**
- "How many vacation days do I have left?" → leave_management_agent (requesting personal leave data)
- "Show me my team's pending leave approvals" → leave_management_agent (manager reviewing team leave)
- "I need to take time off next week" → leave_management_agent (leave request intent)

**Profile/Employee Data Intent Patterns:**
- "What is my job title?" → profile_agent (requesting personal employment data)
- "List all my direct reports" → profile_agent (requesting team structure data)
- "Who is my manager?" → profile_agent (requesting organizational hierarchy)

**Attendance Tracking Intent Patterns:**
- "Did I check in today?" → attendance_management_agent (requesting attendance status)
- "Show my attendance for last month" → attendance_management_agent (requesting attendance history)

**Policy Information Intent Patterns (KEY - these demonstrate semantic understanding):**
- "I am new joiner, wanted to understand what documents are required for background verification?" → policy_agent
  (Intent: New employee seeking process requirements - matches pattern #3 and #6)

- "How long does the verification process take?" → policy_agent
  (Intent: Timeline inquiry about company process - matches pattern #4)

- "Can I start work before my background check is complete?" → policy_agent
  (Intent: Permission/authorization request - matches pattern #5)

- "What should I wear to the office?" → policy_agent
  (Intent: Seeking company standards - matches pattern #1, no "policy" keyword needed)

- "Am I allowed to work from home on Fridays?" → policy_agent
  (Intent: Permission request - matches pattern #5, no "policy" keyword needed)

- "How do I submit my expenses?" → policy_agent
  (Intent: Process inquiry - matches pattern #2, no "policy" keyword needed)

**Non-HR Intent Patterns:**
- "Tell me a joke" → root_agent (non-HR request)
- "What's the weather like?" → root_agent (non-HR request)
- "Show my team's leave history" → leave_management_agent
- "Show my profile" → profile_agent
- "Show my team's comp-off pending approvals" → leave_management_agent
- "Show me the holiday calendar" → leave_management_agent
- "Show me my department" → profile_agent
- "Show me the HR guidelines for annual leave" → policy_agent
- "Show me my attendance anomalies" → attendance_management_agent
- "Show me my team" → profile_agent
- "Show me my team's leave overview" → leave_management_agent
- "Show me my team's pending approvals" → leave_management_agent
- "Show me my work location" → profile_agent
- "Show me my team structure" → profile_agent
- "Show me my team member's leave requests" → leave_management_agent
- "Show me my team member's profile" → profile_agent
- "what is posh policy" → policy_agent
- "what is the policy for comp-off" → policy_agent
- "what is the policy for sick leave" → policy_agent
- "what is the policy for maternity leave" → policy_agent
- "what is the policy for paternity leave" → policy_agent

User message: "{message}"

Return ONLY a valid JSON object in this format (no extra text):
{{
    "agent": "agent_name",
    "type": "intent_type",
    "confidence": 0.0-1.0,
    "reason": "brief explanation"
}}
"""

            # Get response from LLM
            response = model.generate_content(intent_prompt)
            response_text = response.text.strip()

            # Parse JSON response
            try:
                result = json.loads(response_text)
                return result
            except json.JSONDecodeError:
                logger.error("Failed to parse LLM response as JSON")
                return {
                    "agent": "root_agent",
                    "type": "general",
                    "confidence": 0.0,
                    "reason": "Failed to parse response"
                }

        except Exception as e:
            logger.error(f"Error in LLM intent analysis: {str(e)}")
            # Fallback to keyword-based routing if LLM fails
            message_lower = message.lower()
            
            # Enhanced semantic pattern fallback when LLM fails
            agent_keywords = {
                "leave_management_agent": [
                    'leave', 'vacation', 'holiday', 'time off', 'pto', 'sick leave',
                    'annual leave', 'comp off', 'leave balance', 'leave request'
                ],
                "profile_agent": [
                    'profile', 'job title', 'department', 'manager', 'employee id',
                    'team members', 'direct reports', 'my information'
                ],
                "policy_agent": [
                    # Core policy indicators
                    'policy', 'procedure', 'guideline', 'rule', 'compliance',
                    # Permission/authorization patterns
                    'can i', 'am i allowed', 'is it okay', 'what is allowed',
                    # Process inquiry patterns
                    'how to', 'how do i', 'what should i do', 'process for', 'steps for',
                    # Document/requirement patterns
                    'what documents', 'documents required', 'what do i need', 'documents needed',
                    # Company standards patterns
                    'company rules', 'what are the rules', 'guidelines for',
                    # Background verification (common case)
                    'background verification', 'verification', 'documents for verification',
                    # Timeline patterns
                    'how long', 'when will', 'how much time', 'timeline',
                    # New employee patterns
                    'new joiner', 'first day', 'onboarding', 'what to bring'
                ],
                "attendance_management_agent": [
                    'attendance', 'check-in', 'check-out', 'present', 'absent'
                ],
                "hr_service_desk_agent": [
                    'hr support', 'help', 'issue', 'problem'
                ]
            }

            # Check which agent's keywords match, with leave management keywords taking highest precedence
            for agent, keywords in agent_keywords.items():
                if any(keyword in message_lower for keyword in keywords):
                    return {
                        "agent": agent,
                        "type": agent.replace('_agent', ''),
                        "confidence": 0.7,  # Lower confidence for keyword-based routing
                        "reason": "Keyword-based routing due to LLM failure"
                    }

            # Default to root agent if no keywords match
            return {
                "agent": "root_agent",
                "type": "general",
                "confidence": 0.5,
                "reason": "No specific intent detected, defaulting to root agent"
            }

    def _is_tool_execution_detail(self, text: str) -> bool:
        """Check if the text contains internal tool execution details.

        Args:
            text: The text to check

        Returns:
            True if the text contains tool execution details
        """
        if not text:
            return False

        text_lower = text.lower()

        # Check for various patterns that indicate tool execution details
        patterns = [
            "called tool",
            "with parameters:",
            "for context:",
            "[policy_agent]",
            "[leave_management_agent]",
            "[profile_agent]",
            "[attendance_management_agent]",
            "tool execution",
            "function call",
            "api call with"
        ]

        return any(pattern in text_lower for pattern in patterns)

    def _get_filtered_response(self, original_text: str) -> str:
        """Get a filtered response to replace tool execution details.

        Args:
            original_text: The original text containing tool execution details

        Returns:
            A user-friendly response
        """
        # Determine the type of tool being called and provide appropriate response
        if "get_policy_information" in original_text:
            return "I'm looking up the policy information for you. Please wait a moment while I search our policy database."
        elif "get_leave_balance" in original_text:
            return "I'm retrieving your leave balance information. Please wait a moment."
        elif "get_employee_info" in original_text:
            return "I'm fetching your profile information. Please wait a moment."
        elif "get_my_team" in original_text:
            return "I'm retrieving your team information. Please wait a moment."
        elif "search_employee" in original_text:
            return "I'm searching for employee information. Please wait a moment."
        elif "apply_leave" in original_text:
            return "I'm processing your leave application. Please wait a moment."
        else:
            return "I'm processing your request. Please wait a moment while I retrieve the information."
