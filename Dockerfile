FROM python:3.12-slim

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app

WORKDIR /app

# Install required build tools
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir --upgrade pip wheel && \
    pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create simple startup script
RUN echo '#!/bin/bash' > /app/start.sh && \
    echo 'set -e' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Handle Google credentials' >> /app/start.sh && \
    echo 'if [ ! -z "$GOOGLE_APPLICATION_CREDENTIALS_JSON" ]; then' >> /app/start.sh && \
    echo '    echo "=== Google Credentials Setup ==="' >> /app/start.sh && \
    echo '    echo "Stringified JSON input length: ${#GOOGLE_APPLICATION_CREDENTIALS_JSON} characters"' >> /app/start.sh && \
    echo '    echo "JSON input (first 100 chars): ${GOOGLE_APPLICATION_CREDENTIALS_JSON:0:100}"' >> /app/start.sh && \
    echo '    ' >> /app/start.sh && \
    echo '    # Parse stringified JSON and write to file using Python' >> /app/start.sh && \
    echo '    python3 -c "' >> /app/start.sh && \
    echo 'import json, os' >> /app/start.sh && \
    echo 'try:' >> /app/start.sh && \
    echo '    creds_json = os.environ.get(\"GOOGLE_APPLICATION_CREDENTIALS_JSON\", \"\")' >> /app/start.sh && \
    echo '    if not creds_json:' >> /app/start.sh && \
    echo '        raise ValueError(\"Empty credentials JSON string\")' >> /app/start.sh && \
    echo '    ' >> /app/start.sh && \
    echo '    print(\"Processing stringified JSON of length:\", len(creds_json))' >> /app/start.sh && \
    echo '    ' >> /app/start.sh && \
    echo '    # Parse and validate JSON' >> /app/start.sh && \
    echo '    creds_data = json.loads(creds_json)' >> /app/start.sh && \
    echo '    ' >> /app/start.sh && \
    echo '    # Validate required fields' >> /app/start.sh && \
    echo '    required_fields = [\"private_key\", \"client_email\", \"project_id\", \"type\"]' >> /app/start.sh && \
    echo '    for field in required_fields:' >> /app/start.sh && \
    echo '        if field not in creds_data:' >> /app/start.sh && \
    echo '            raise ValueError(\"Missing required field: \" + field)' >> /app/start.sh && \
    echo '    ' >> /app/start.sh && \
    echo '    # Write to file with proper formatting' >> /app/start.sh && \
    echo '    with open(\"/app/google-credentials.json\", \"w\") as f:' >> /app/start.sh && \
    echo '        json.dump(creds_data, f, indent=2)' >> /app/start.sh && \
    echo '    ' >> /app/start.sh && \
    echo '    print(\"✓ Google credentials file created successfully\")' >> /app/start.sh && \
    echo '    print(\"✓ Project ID:\", creds_data.get(\"project_id\"))' >> /app/start.sh && \
    echo '    print(\"✓ Client Email:\", creds_data.get(\"client_email\"))' >> /app/start.sh && \
    echo 'except Exception as e:' >> /app/start.sh && \
    echo '    print(\"✗ Error processing credentials:\", str(e))' >> /app/start.sh && \
    echo '    exit(1)' >> /app/start.sh && \
    echo '"' >> /app/start.sh && \
    echo '    ' >> /app/start.sh && \
    echo '    export GOOGLE_APPLICATION_CREDENTIALS=/app/google-credentials.json' >> /app/start.sh && \
    echo '    echo "✓ Google credentials environment variable set: $GOOGLE_APPLICATION_CREDENTIALS"' >> /app/start.sh && \
    echo 'elif [ -f "/app/google-credentials.json" ]; then' >> /app/start.sh && \
    echo '    export GOOGLE_APPLICATION_CREDENTIALS=/app/google-credentials.json' >> /app/start.sh && \
    echo '    echo "✓ Using existing Google credentials file"' >> /app/start.sh && \
    echo '    echo "File content:"' >> /app/start.sh && \
    echo '    cat /app/google-credentials.json' >> /app/start.sh && \
    echo 'else' >> /app/start.sh && \
    echo '    echo "⚠ No Google credentials found"' >> /app/start.sh && \
    echo 'fi' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Run diagnostic check if credentials are available' >> /app/start.sh && \
    echo 'if [ -f "/app/google-credentials.json" ] && [ ! -z "$GOOGLE_CLOUD_PROJECT" ]; then' >> /app/start.sh && \
    echo '    echo "=== Running Google Cloud Diagnostics ==="' >> /app/start.sh && \
    echo '    python3 /app/debug_google_credentials.py || echo "Diagnostic completed with warnings"' >> /app/start.sh && \
    echo '    echo "=== End Diagnostics ==="' >> /app/start.sh && \
    echo 'fi' >> /app/start.sh && \
    echo '' >> /app/start.sh && \
    echo '# Start the application' >> /app/start.sh && \
    echo 'echo "Starting HR Solution API server..."' >> /app/start.sh && \
    echo 'exec uvicorn api_server:app --host 0.0.0.0 --port 8081' >> /app/start.sh && \
    chmod +x /app/start.sh

EXPOSE 8081

CMD ["/app/start.sh"]
 