#!/usr/bin/env python3
"""
Debug script for Google Cloud credentials in the container.
This script helps diagnose credential issues during deployment.
"""

import os
import json
import base64
import sys
from pathlib import Path


def check_environment_variables():
    """Check Google Cloud related environment variables."""
    print("=== Environment Variables ===")
    
    env_vars = [
        'GOOGLE_APPLICATION_CREDENTIALS',
        'GOOGLE_APPLICATION_CREDENTIALS_JSON',
        'GOOGLE_CLOUD_PROJECT',
        'GOOGLE_CLOUD_LOCATION',
        'GOOGLE_API_KEY',
        'GOOGLE_GENAI_USE_VERTEXAI'
    ]
    
    for var in env_vars:
        value = os.environ.get(var)
        if value:
            if var == 'GOOGLE_APPLICATION_CREDENTIALS_JSON':
                print(f"{var}: SET (length: {len(value)} characters)")
            elif var == 'GOOGLE_API_KEY':
                print(f"{var}: SET (length: {len(value)} characters)")
            else:
                print(f"{var}: {value}")
        else:
            print(f"{var}: NOT SET")
    print()


def check_credentials_file():
    """Check the Google credentials file."""
    print("=== Credentials File Check ===")
    
    creds_path = "/app/google-credentials.json"
    
    if not os.path.exists(creds_path):
        print(f"✗ {creds_path} does not exist")
        return False
    
    print(f"✓ {creds_path} exists")
    
    # Check file size
    file_size = os.path.getsize(creds_path)
    print(f"File size: {file_size} bytes")
    
    if file_size == 0:
        print("✗ File is empty")
        return False
    
    # Check if it's valid JSON
    try:
        with open(creds_path, 'r') as f:
            creds_data = json.load(f)
        print("✓ File contains valid JSON")
    except json.JSONDecodeError as e:
        print(f"✗ Invalid JSON: {e}")
        return False
    except Exception as e:
        print(f"✗ Error reading file: {e}")
        return False
    
    # Check required fields
    required_fields = ['type', 'project_id', 'private_key_id', 'private_key', 'client_email', 'client_id']
    missing_fields = []
    
    for field in required_fields:
        if field not in creds_data:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"✗ Missing required fields: {', '.join(missing_fields)}")
        return False
    
    print("✓ All required fields present")
    
    # Check private key format
    private_key = creds_data.get('private_key', '')
    if not private_key.startswith('-----BEGIN PRIVATE KEY-----'):
        print("✗ Private key does not start with proper PEM header")
        return False
    
    if not private_key.endswith('-----END PRIVATE KEY-----\n'):
        print("✗ Private key does not end with proper PEM footer")
        return False
    
    print("✓ Private key has correct PEM format")
    
    # Check for common corruption issues
    if '\\n' in private_key:
        print("✗ Private key contains literal \\n instead of actual newlines")
        return False
    
    print("✓ Private key format appears correct")
    
    print(f"Project ID: {creds_data.get('project_id')}")
    print(f"Client Email: {creds_data.get('client_email')}")
    print(f"Private Key ID: {creds_data.get('private_key_id')}")
    
    return True


def test_google_auth():
    """Test Google Cloud authentication."""
    print("=== Google Cloud Authentication Test ===")
    
    try:
        import google.auth
        from google.auth.exceptions import DefaultCredentialsError
        
        credentials, project = google.auth.default()
        print("✓ Google Cloud authentication successful")
        print(f"Project: {project}")
        print(f"Credentials type: {type(credentials).__name__}")
        
        return True
        
    except DefaultCredentialsError as e:
        print(f"✗ Default credentials error: {e}")
        return False
    except Exception as e:
        print(f"✗ Authentication failed: {e}")
        return False


def test_vertex_ai():
    """Test Vertex AI connection."""
    print("=== Vertex AI Test ===")
    
    try:
        from vertexai.language_models import TextEmbeddingModel
        
        model_name = os.environ.get("GOOGLE_EMBEDDING_MODEL", "textembedding-gecko@003")
        print(f"Testing model: {model_name}")
        
        model = TextEmbeddingModel.from_pretrained(model_name)
        print("✓ Vertex AI model loaded successfully")
        
        # Test embedding generation
        test_text = "Hello, world!"
        embeddings = model.get_embeddings([test_text])
        print(f"✓ Generated embedding for test text (dimension: {len(embeddings[0].values)})")
        
        return True
        
    except Exception as e:
        print(f"✗ Vertex AI test failed: {e}")
        return False


def main():
    """Main debug function."""
    print("=== Google Cloud Credentials Debug Script ===")
    print()
    
    # Run all checks
    checks = [
        ("Environment Variables", check_environment_variables),
        ("Credentials File", check_credentials_file),
        ("Google Auth", test_google_auth),
        ("Vertex AI", test_vertex_ai)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            results[check_name] = result
        except Exception as e:
            print(f"✗ {check_name} check failed with exception: {e}")
            results[check_name] = False
        print()
    
    # Summary
    print("=== Summary ===")
    all_passed = True
    for check_name, result in results.items():
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{check_name}: {status}")
        if not result:
            all_passed = False
    
    print()
    if all_passed:
        print("🎉 All checks passed! Google Cloud credentials are working correctly.")
        sys.exit(0)
    else:
        print("❌ Some checks failed. Please review the output above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
