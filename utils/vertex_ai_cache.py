import logging
import time
from functools import lru_cache
from vertexai.language_models import TextEmbeddingModel
import os
from dotenv import load_dotenv

load_dotenv()
logger = logging.getLogger(__name__)


@lru_cache(maxsize=1)
def get_embedding_model():
    """
    Get Vertex AI embedding model with LRU cache.
    This ensures the model is loaded only once per session.
    
    Returns:
        TextEmbeddingModel instance
    """
    model_name = os.environ.get("GOOGLE_EMBEDDING_MODEL", "textembedding-gecko@003")
    logger.info(f"Loading Vertex AI model: {model_name}")
    
    start_time = time.time()
    try:
        model = TextEmbeddingModel.from_pretrained(model_name)
        load_time = time.time() - start_time
        logger.info(f"Model loaded successfully in {load_time:.2f} seconds")
        return model
    except Exception as e:
        logger.error(f"Failed to load model: {str(e)}")
        raise


def generate_embedding_cached(text: str) -> list:
    """
    Generate embedding using cached model.
    
    Args:
        text: The input text to embed
        
    Returns:
        Embedding vector
    """
    start_time = time.time()
    
    try:
        model = get_embedding_model()
        embedding = model.get_embeddings([text])[0]
        elapsed_time = time.time() - start_time
        
        logger.info(f"Embedding generated in {elapsed_time:.2f} seconds")
        return embedding.values
        
    except Exception as e:
        logger.error(f"Embedding generation failed: {str(e)}")
        raise


def clear_model_cache():
    """
    Clear the model cache (useful for testing or memory management).
    """
    get_embedding_model.cache_clear()
    logger.info("Vertex AI model cache cleared")


def get_cache_info():
    """
    Get cache information.
    
    Returns:
        Dictionary with cache statistics
    """
    cache_info = get_embedding_model.cache_info()
    return {
        'hits': cache_info.hits,
        'misses': cache_info.misses,
        'maxsize': cache_info.maxsize,
        'currsize': cache_info.currsize
    } 